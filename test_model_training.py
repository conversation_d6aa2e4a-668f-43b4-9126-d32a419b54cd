#!/usr/bin/env python3
"""
Test script to verify model training and CSV generation
This script runs a simplified version of the training process for testing
"""

import os
import sys
import pandas as pd
import numpy as np

# Add the AI directory to the path so we can import the functions
sys.path.append('AI')

def test_data_loading():
    """Test the data loading functionality"""
    print("🔍 Testing data loading...")
    
    try:
        # Import the load_data function
        from bitcoin_price_prediction_using_lstm import load_data
        
        # Load the data
        maindf = load_data()
        
        print(f"✅ Data loaded successfully:")
        print(f"   Shape: {maindf.shape}")
        print(f"   Columns: {list(maindf.columns)}")
        print(f"   Date range: {maindf['Date'].min()} to {maindf['Date'].max()}")
        
        return maindf
        
    except Exception as e:
        print(f"❌ Error loading data: {str(e)}")
        return None

def test_csv_generation_simple():
    """Test CSV generation with a simple mock model"""
    print("\n🔍 Testing CSV generation with mock data...")
    
    try:
        # Create mock prediction data
        from datetime import datetime, timedelta
        
        # Generate 100 days of mock predictions for testing
        start_date = datetime.now() + timedelta(days=1)
        dates = []
        prices = []
        
        base_price = 67000
        for i in range(100):
            date = start_date + timedelta(days=i)
            dates.append(date.strftime('%Y-%m-%d'))
            # Simple mock price with some variation
            price = base_price + (i * 10) + np.random.normal(0, 100)
            prices.append(max(price, 1000))  # Ensure positive price
        
        # Create CSV
        csv_path = 'Data/test_predictions.csv'
        os.makedirs('Data', exist_ok=True)
        
        import csv
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Date', 'Predicted_Price'])
            for date, price in zip(dates, prices):
                writer.writerow([date, f"{price:.2f}"])
        
        # Verify the CSV
        df = pd.read_csv(csv_path)
        print(f"✅ Mock CSV generated successfully:")
        print(f"   File: {csv_path}")
        print(f"   Records: {len(df)}")
        print(f"   Date range: {df['Date'].iloc[0]} to {df['Date'].iloc[-1]}")
        print(f"   Price range: ${df['Predicted_Price'].min():.2f} - ${df['Predicted_Price'].max():.2f}")
        
        return csv_path
        
    except Exception as e:
        print(f"❌ Error generating mock CSV: {str(e)}")
        return None

def test_model_files():
    """Test if model files exist and are accessible"""
    print("\n🔍 Testing model files...")
    
    model_files = [
        'AI/model/bitcoin_advanced_multivariate_lstm.keras',
        'AI/model/bitcoin_price_scaler.save',
        'AI/model/bitcoin_volume_scaler.save'
    ]
    
    all_exist = True
    for file_path in model_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size} bytes)")
        else:
            print(f"❌ Missing: {file_path}")
            all_exist = False
    
    return all_exist

def test_frontend_csv_copy():
    """Test copying CSV to frontend directory"""
    print("\n🔍 Testing frontend CSV copy...")
    
    try:
        source_path = 'Data/test_predictions.csv'
        if not os.path.exists(source_path):
            print(f"❌ Source file not found: {source_path}")
            return False
        
        # Copy to frontend
        import shutil
        frontend_dir = 'Frontend/public/Data'
        os.makedirs(frontend_dir, exist_ok=True)
        
        dest_path = os.path.join(frontend_dir, 'bitcoin_predictions.csv')
        shutil.copy2(source_path, dest_path)
        
        if os.path.exists(dest_path):
            print(f"✅ CSV copied to frontend: {dest_path}")
            return True
        else:
            print(f"❌ Failed to copy CSV to frontend")
            return False
            
    except Exception as e:
        print(f"❌ Error copying CSV to frontend: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🧪 Model Training and CSV Generation Test Suite")
    print("=" * 60)
    
    tests = [
        ("Data Loading", test_data_loading),
        ("Model Files", test_model_files),
        ("CSV Generation", test_csv_generation_simple),
        ("Frontend CSV Copy", test_frontend_csv_copy)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            # Convert result to boolean if it's not already
            success = bool(result) if result is not None else False
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! You can now test the frontend.")
        print("\nNext steps:")
        print("1. cd Frontend")
        print("2. npm start")
        print("3. Open http://localhost:3000")
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed.")
        
        if not any(name == "Model Files" and result for name, result in results):
            print("\n💡 To fix model file issues:")
            print("   Run: python AI/bitcoin_price_prediction_using_lstm.py")

if __name__ == "__main__":
    main()
